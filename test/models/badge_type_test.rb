require 'test_helper'

class BadgeTypeTest < ActiveSupport::TestCase
  def setup_environment
    # Disable color contrast validation for tests
    ENV['VALIDATE_BADGE_COLOR_CONTRAST'] = 'false'
  end

  def teardown_environment
    # Re-enable color contrast validation after tests
    ENV['VALIDATE_BADGE_COLOR_CONTRAST'] = 'true'
  end
  def setup
    setup_environment
    @badge_type =
      BadgeType.new(
        name: 'Test Badge',
        description: 'A test badge for unit testing',
        background_color: '#000000',
        text_color: '#ffffff',
        icon: 'check-circle',
        priority: 1,
      )
  end

  def teardown
    # Clean up badge assignments to avoid foreign key constraint issues
    begin
      BadgeAssignment.delete_all
    rescue ActiveRecord::StatementInvalid
      # If we're in a failed transaction, rollback and try again
      ActiveRecord::Base.connection.rollback_transaction rescue nil
      BadgeAssignment.delete_all rescue nil
    end
    teardown_environment
  end

  test 'should be valid with valid attributes' do
    assert @badge_type.valid?
  end

  test 'should require name' do
    @badge_type.name = nil
    assert_not @badge_type.valid?
    assert_includes @badge_type.errors[:name], "can't be blank"
  end

  test 'should require unique name' do
    @badge_type.save!
    duplicate =
      BadgeType.new(
        name: 'Test Badge',
        description: 'Another test badge',
        background_color: '#000000',
        text_color: '#ffffff',
        icon: 'star',
      )
    assert_not duplicate.valid?
    assert_includes duplicate.errors[:name], 'has already been taken'
  end

  test 'should limit name length' do
    @badge_type.name = 'a' * 51
    assert_not @badge_type.valid?
    assert_includes @badge_type.errors[:name],
                    'is too long (maximum is 50 characters)'
  end

  test 'should require description' do
    @badge_type.description = nil
    assert_not @badge_type.valid?
    assert_includes @badge_type.errors[:description], "can't be blank"
  end

  test 'should limit description length' do
    @badge_type.description = 'a' * 501
    assert_not @badge_type.valid?
    assert_includes @badge_type.errors[:description],
                    'is too long (maximum is 500 characters)'
  end

  test 'should require valid background color format' do
    invalid_colors = %w[blue #gggggg #12345 ffffff #1234567]

    invalid_colors.each do |color|
      @badge_type.background_color = color
      assert_not @badge_type.valid?, "#{color} should be invalid"
      assert_includes @badge_type.errors[:background_color],
                      'must be a valid hex color code (e.g., #ffffff or #fff)'
    end
  end

  test 'should accept valid background color formats' do
    valid_colors = %w[#ffffff #000000 #3b82f6 #fff #000 #f00]

    valid_colors.each do |color|
      @badge_type.background_color = color

      # Use contrasting text color based on background
      @badge_type.text_color =
        (color == '#ffffff' || color == '#fff') ? '#000000' : '#ffffff'
      @badge_type.valid? # Run validations
      assert_not_includes @badge_type.errors[:background_color],
                          'must be a valid hex color code (e.g., #ffffff or #fff)',
                          "#{color} should be valid"
    end
  end

  test 'should require valid text color format' do
    invalid_colors = %w[white #gggggg #12345 000000 #1234567]

    invalid_colors.each do |color|
      @badge_type.text_color = color
      assert_not @badge_type.valid?, "#{color} should be invalid"
      assert_includes @badge_type.errors[:text_color],
                      'must be a valid hex color code (e.g., #000000 or #000)'
    end
  end

  test 'should require icon' do
    @badge_type.icon = nil
    assert_not @badge_type.valid?
    assert_includes @badge_type.errors[:icon], "can't be blank"
  end

  test 'should limit icon length' do
    @badge_type.icon = 'a' * 101
    assert_not @badge_type.valid?
    assert_includes @badge_type.errors[:icon],
                    'is too long (maximum is 100 characters)'
  end

  test 'should require priority' do
    @badge_type.priority = nil
    assert_not @badge_type.valid?
    assert_includes @badge_type.errors[:priority], "can't be blank"
  end

  test 'should require non-negative priority' do
    @badge_type.priority = -1
    assert_not @badge_type.valid?
    assert_includes @badge_type.errors[:priority],
                    'must be greater than or equal to 0'
  end

  test 'should default to active' do
    badge_type = BadgeType.new
    assert badge_type.active
  end

  test 'should default priority to 0' do
    badge_type = BadgeType.new
    assert_equal 0, badge_type.priority
  end

  test 'should validate color contrast when enabled' do
    # Temporarily enable color contrast validation
    ENV['VALIDATE_BADGE_COLOR_CONTRAST'] = 'true'

    # Low contrast combination (white text on light background)
    badge =
      BadgeType.new(
        name: 'Low Contrast Badge',
        description: 'A badge with poor contrast',
        background_color: '#f0f0f0',
        text_color: '#ffffff',
        icon: 'test',
        priority: 1,
      )

    assert_not badge.valid?
    assert_includes badge.errors[:base],
                    'Text and background colors must have sufficient contrast for accessibility (minimum 4.5:1 ratio)'

    # Reset environment
    ENV['VALIDATE_BADGE_COLOR_CONTRAST'] = 'false'
  end

  test 'should accept good color contrast when validation enabled' do
    # Temporarily enable color contrast validation
    ENV['VALIDATE_BADGE_COLOR_CONTRAST'] = 'true'

    # High contrast combination (white text on dark background)
    badge =
      BadgeType.new(
        name: 'High Contrast Badge',
        description: 'A badge with good contrast',
        background_color: '#000000',
        text_color: '#ffffff',
        icon: 'test',
        priority: 1,
      )

    assert badge.valid?

    # Reset environment
    ENV['VALIDATE_BADGE_COLOR_CONTRAST'] = 'false'
  end

  test 'active scope should return only active badges' do
    # Clear existing badge assignments first to avoid foreign key constraints
    BadgeAssignment.delete_all
    # Clear existing badges to avoid conflicts
    BadgeType.delete_all

    active_badge =
      BadgeType.create!(
        name: 'Active Badge Test',
        description: 'An active badge',
        background_color: '#000000',
        text_color: '#ffffff',
        icon: 'star',
        active: true,
      )

    inactive_badge =
      BadgeType.create!(
        name: 'Inactive Badge Test',
        description: 'An inactive badge',
        background_color: '#000000',
        text_color: '#ffffff',
        icon: 'x-circle',
        active: false,
      )

    active_badges = BadgeType.active
    assert_includes active_badges, active_badge
    assert_not_includes active_badges, inactive_badge
  end

  test 'by_priority scope should order by priority then name' do
    # Clear existing badge assignments first to avoid foreign key constraints
    BadgeAssignment.delete_all
    # Clear existing badges to avoid conflicts
    BadgeType.delete_all

    badge1 =
      BadgeType.create!(
        name: 'Z Badge Test',
        description: 'Last badge',
        background_color: '#000000',
        text_color: '#ffffff',
        icon: 'star',
        priority: 1,
      )

    badge2 =
      BadgeType.create!(
        name: 'A Badge Test',
        description: 'First badge',
        background_color: '#000000',
        text_color: '#ffffff',
        icon: 'check',
        priority: 1,
      )

    badge3 =
      BadgeType.create!(
        name: 'Priority Badge Test',
        description: 'High priority badge',
        background_color: '#000000',
        text_color: '#ffffff',
        icon: 'star',
        priority: 0,
      )

    ordered_badges = BadgeType.by_priority
    assert_equal [badge3, badge2, badge1], ordered_badges.to_a
  end

  test 'should normalize 3-digit hex colors' do
    @badge_type.background_color = '#f00'
    @badge_type.text_color = '#fff'

    assert_equal '#ff0000', @badge_type.normalized_background_color
    assert_equal '#ffffff', @badge_type.normalized_text_color
  end

  test 'should return css styles hash' do
    expected_styles = { 'background-color' => '#000000', 'color' => '#ffffff' }

    assert_equal expected_styles, @badge_type.css_styles
  end

  test 'can_be_deleted should return true when no assignments exist' do
    @badge_type.save!
    assert @badge_type.can_be_deleted?
  end

  test 'should restrict deletion when badge assignments exist' do
    @badge_type.save!
    user = users(:talent)
    admin = users(:super_admin)

    assignment =
      BadgeAssignment.create!(
        badge_type: @badge_type,
        user: user,
        admin: admin,
        assigned_at: Time.current,
      )

    # Verify the assignment was created and associated
    assert assignment.persisted?
    assert_equal @badge_type.id, assignment.badge_type_id
    assert_equal 1, @badge_type.badge_assignments.count
    assert_not @badge_type.can_be_deleted?

    # dependent: :restrict_with_error should return false and add errors instead of raising exception
    result = @badge_type.destroy
    assert_equal false, result
    assert_not @badge_type.destroyed?
    assert @badge_type.errors.any?
  end

  test 'assignment_count should return correct count' do
    @badge_type.save!
    assert_equal 0, @badge_type.assignment_count

    user = users(:talent)
    admin = users(:super_admin)

    BadgeAssignment.create!(
      badge_type: @badge_type,
      user: user,
      admin: admin,
      assigned_at: Time.current,
    )

    assert_equal 1, @badge_type.reload.assignment_count
  end

  test 'active_assignment_count should return correct count' do
    @badge_type.save!
    user = users(:talent)
    admin = users(:super_admin)

    # Create active assignment
    BadgeAssignment.create!(
      badge_type: @badge_type,
      user: user,
      admin: admin,
      assigned_at: Time.current,
    )

    # Create expired assignment
    expired_assignment =
      BadgeAssignment.new(
        badge_type: @badge_type,
        user: users(:scout),
        admin: admin,
        assigned_at: 2.days.ago,
        expires_at: 1.day.ago,
      ).allow_expired_for_testing!
    expired_assignment.save!

    assert_equal 1, @badge_type.reload.active_assignment_count
  end

  test 'assigned_users_count should return distinct user count' do
    @badge_type.save!
    user = users(:talent)
    admin = users(:super_admin)

    BadgeAssignment.create!(
      badge_type: @badge_type,
      user: user,
      admin: admin,
      assigned_at: Time.current,
    )

    assert_equal 1, @badge_type.reload.assigned_users_count
  end
end
