<%= form_with(model: [:super_admin, badge_type],
                class: "space-y-8 divide-y divide-gray-200",
                data: {
                  controller: "badge-preview",
                  badge_preview_default_name_value: badge_type.name.presence || "Sample Badge",
                  badge_preview_default_description_value: badge_type.description.presence || "This is a sample badge description",
                  badge_preview_default_background_color_value: badge_type.background_color.presence || "#3B82F6",
                  badge_preview_default_text_color_value: badge_type.text_color.presence || "#FFFFFF",
                  badge_preview_default_icon_value: badge_type.icon.presence || "ph-star-fill"
                }) do |form| %>
  <% if badge_type.errors.any? %>
    <div class="p-4 rounded-md bg-red-50">
      <div class="flex">
        <div class="flex-shrink-0">
          <!-- Heroicon name: solid/x-circle -->
          <svg class="w-5 h-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800"><%= pluralize(badge_type.errors.count, "error") %> prohibited this badge type from being saved:</h3>
          <div class="mt-2 text-sm text-red-700">
            <ul role="list" class="pl-5 space-y-1 list-disc">
              <% badge_type.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <div class="space-y-8 divide-y divide-gray-200 sm:space-y-5">
    <div>
      <div>
        <h3 class="text-lg font-medium leading-6 text-gray-900">Badge Details</h3>
        <p class="max-w-2xl mt-1 text-sm text-gray-500">This information will be displayed publicly so be careful what you share.</p>
      </div>

      <div class="mt-6 space-y-6 sm:mt-5 sm:space-y-5">
        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= form.label :name, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <%= form.text_field :name,
                class: "max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md",
                data: { badge_preview_target: "name" } %>
          </div>
        </div>

        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= form.label :description, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <%= form.text_area :description,
                rows: 3,
                class: "max-w-lg shadow-sm block w-full focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border border-gray-300 rounded-md",
                data: { badge_preview_target: "description" } %>
            <p class="mt-2 text-sm text-gray-500">Write a few sentences about the badge.</p>
          </div>
        </div>
      </div>
    </div>

    <div class="pt-8 space-y-6 sm:pt-10 sm:space-y-5">
      <div>
        <h3 class="text-lg font-medium leading-6 text-gray-900">Appearance</h3>
        <p class="max-w-2xl mt-1 text-sm text-gray-500">Customize the look and feel of the badge.</p>
      </div>
      <div class="space-y-6 sm:space-y-5">
        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= form.label :background_color, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <%= form.color_field :background_color,
                class: "w-20 h-10",
                data: { badge_preview_target: "backgroundColor" } %>
          </div>
        </div>

        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= form.label :text_color, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <%= form.color_field :text_color,
                class: "w-20 h-10",
                data: { badge_preview_target: "textColor" } %>
          </div>
        </div>

        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= form.label :icon, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <%= form.text_field :icon,
                class: "max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md",
                data: { badge_preview_target: "icon" } %>
            <p class="mt-2 text-sm text-gray-500">Enter a Phosphor icon class (e.g., 'ph-star-fill').</p>
          </div>
        </div>
      </div>
    </div>

    <div class="pt-8 space-y-6 sm:pt-10 sm:space-y-5">
      <div>
        <h3 class="text-lg font-medium leading-6 text-gray-900">Settings</h3>
      </div>
      <div class="space-y-6 sm:space-y-5">
        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= form.label :priority, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <%= form.number_field :priority,
                class: "max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md",
                data: { badge_preview_target: "priority" } %>
            <p class="mt-2 text-sm text-gray-500">Lower numbers display first.</p>
          </div>
        </div>

        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= form.label :active, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <div class="flex items-center">
              <%= form.check_box :active, class: "h-4 w-4 text-indigo-600 border-gray-300 rounded" %>
              <span class="ml-2 text-sm text-gray-900">Active</span>
            </div>
            <p class="mt-2 text-sm text-gray-500">Inactive badges cannot be assigned.</p>
          </div>
        </div>
      </div>
    </div>

    <%# Badge Preview Section %>
    <div class="pt-8 space-y-6 sm:pt-10 sm:space-y-5">
      <div>
        <h3 class="text-lg font-medium leading-6 text-gray-900">Preview</h3>
        <p class="max-w-2xl mt-1 text-sm text-gray-500">See how your badge will appear to users.</p>
      </div>
      <div data-badge-preview-target="preview">
        <%= render 'preview' %>
      </div>
    </div>
  </div>

  <div class="pt-5">
    <div class="flex justify-end">
      <%= link_to 'Cancel', super_admin_badge_types_path, class: "bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
      <%= form.submit class: "ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
    </div>
  </div>
<% end %>