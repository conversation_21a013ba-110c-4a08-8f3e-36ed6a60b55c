<% content_for :title, @badge_type.name %>

<div class="container mx-auto px-4">
  <div class="flex items-center justify-between mb-4">
    <h1 class="text-2xl font-bold">Badge: <%= @badge_type.name %></h1>
    <div class="flex gap-2">
      <%= link_to "Edit", edit_super_admin_badge_type_path(@badge_type), class: "btn-secondary" %>
      <%= form_with model: [:super_admin, @badge_type], method: :delete, local: true, class: "inline" do |form| %>
        <%= form.submit "Delete", class: "btn-danger",
                        data: { confirm: "Are you sure you want to delete this badge type?" } %>
      <% end %>
      <%= link_to "Back to Badge Types", super_admin_badge_types_path, class: "btn-secondary" %>
    </div>
  </div>

  <%# Enhanced Badge Preview Section %>
  <div class="mb-6">
    <div class="bg-white border border-stone-200 rounded-lg p-6 shadow-sm">
      <div class="mb-4">
        <h3 class="text-lg font-medium text-stone-900 mb-2">Badge Preview</h3>
        <p class="text-sm text-stone-600">Interactive preview showing how this badge appears to users</p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <%# Full Size Badge Preview %>
        <div>
          <h4 class="text-sm font-medium text-stone-700 mb-3">Full Size Badge (Profile Display)</h4>
          <div class="flex items-center justify-center p-8 bg-gradient-to-br from-stone-50 to-stone-100 rounded-lg border border-stone-200">
            <div data-controller="badge">
              <div
                class="badge inline-flex items-center font-medium rounded-lg border shadow-sm backdrop-filter backdrop-blur-sm bg-opacity-90 transition-all duration-200 ease-out transform-gpu will-change-transform cursor-default px-3 py-1.5 text-sm"
                style="background-color: <%= @badge_type.background_color %>; color: <%= @badge_type.text_color %>; border-color: <%= @badge_type.background_color %>;"
                data-badge-target="badge"
                role="img"
                aria-label="<%= html_escape("#{@badge_type.name} badge") %>"
              >
                <i class="badge-icon <%= @badge_type.icon %> text-sm mr-1.5 transition-transform duration-200 ease-out" aria-hidden="true"></i>
                <span class="badge-name font-medium"><%= @badge_type.name %></span>
              </div>
            </div>
          </div>
          <p class="text-xs text-stone-500 mt-2 text-center">Hover to see parallax and tilt effects</p>
        </div>

        <%# Compact Badge Preview %>
        <div>
          <h4 class="text-sm font-medium text-stone-700 mb-3">Compact Size (Search Results)</h4>
          <div class="flex items-center justify-center p-6 bg-gradient-to-br from-stone-50 to-stone-100 rounded-lg border border-stone-200">
            <div data-controller="badge">
              <div
                class="badge-compact inline-flex items-center font-medium rounded-md border shadow-sm backdrop-filter backdrop-blur-sm bg-opacity-90 transition-all duration-200 ease-out transform-gpu will-change-transform cursor-default px-2 py-0.5 text-xs"
                style="background-color: <%= @badge_type.background_color %>; color: <%= @badge_type.text_color %>; border-color: <%= @badge_type.background_color %>; max-width: 120px;"
                data-badge-target="badge"
                role="img"
                aria-label="<%= html_escape("#{@badge_type.name} badge") %>"
              >
                <i class="badge-icon <%= @badge_type.icon %> text-xs mr-1 transition-transform duration-200 ease-out" aria-hidden="true"></i>
                <span class="badge-name font-medium truncate" style="max-width: 80px;"><%= @badge_type.name %></span>
              </div>
            </div>
          </div>
          <p class="text-xs text-stone-500 mt-2 text-center">Hover to see compact badge effects</p>
        </div>
      </div>

      <%# Description Preview %>
      <div class="mt-6">
        <h4 class="text-sm font-medium text-stone-700 mb-3">Description (Tooltip Preview)</h4>
        <div class="p-4 bg-gradient-to-br from-stone-50 to-stone-100 rounded-lg border border-stone-200">
          <div class="relative inline-block">
            <div class="bg-stone-900 text-white text-xs rounded-md px-3 py-2 shadow-lg max-w-xs">
              <p><%= @badge_type.description %></p>
              <%# Tooltip arrow %>
              <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2">
                <div class="w-2 h-2 bg-stone-900 rotate-45"></div>
              </div>
            </div>
          </div>
          <p class="text-xs text-stone-500 mt-3">This appears when users hover over the badge</p>
        </div>
      </div>
    </div>
  </div>

  <%# Badge Details Section %>
  <div class="bg-white shadow-md rounded-lg p-6 mb-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Badge Details</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h4 class="text-md font-medium text-gray-900 mb-3">Basic Information</h4>
        <dl class="space-y-3">
          <div>
            <dt class="text-sm font-medium text-gray-500">Name</dt>
            <dd class="text-sm text-gray-900 font-medium"><%= @badge_type.name %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Description</dt>
            <dd class="text-sm text-gray-900"><%= @badge_type.description %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Priority</dt>
            <dd class="text-sm text-gray-900">
              <%= @badge_type.priority %>
              <span class="text-xs text-gray-500">(lower numbers display first)</span>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Status</dt>
            <dd class="text-sm text-gray-900">
              <% if @badge_type.active? %>
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                  Active
                </span>
              <% else %>
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                  Inactive
                </span>
              <% end %>
            </dd>
          </div>
        </dl>
      </div>
      <div>
        <h4 class="text-md font-medium text-gray-900 mb-3">Appearance</h4>
        <dl class="space-y-3">
          <div>
            <dt class="text-sm font-medium text-gray-500">Background Color</dt>
            <dd class="text-sm text-gray-900 flex items-center">
              <span class="w-5 h-5 rounded border border-gray-300 mr-2 shadow-sm" style="background-color: <%= @badge_type.background_color %>;"></span>
              <span class="font-mono"><%= @badge_type.background_color %></span>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Text Color</dt>
            <dd class="text-sm text-gray-900 flex items-center">
              <span class="w-5 h-5 rounded border border-gray-300 mr-2 shadow-sm" style="background-color: <%= @badge_type.text_color %>;"></span>
              <span class="font-mono"><%= @badge_type.text_color %></span>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Icon Class</dt>
            <dd class="text-sm text-gray-900 flex items-center">
              <i class="<%= @badge_type.icon %> mr-2 text-lg"></i>
              <span class="font-mono"><%= @badge_type.icon %></span>
            </dd>
          </div>
        </dl>
      </div>
    </div>
  </div>

  <h2 class="text-xl font-bold mb-4">Assignments (<%= @assignments.count %>)</h2>

  <div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned By</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned At</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expires At</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% @assignments.each do |assignment| %>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <%= link_to assignment.user.name, super_admin_user_path(assignment.user), class: "text-blue-600 hover:underline" %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap"><%= assignment.admin.name %></td>
            <td class="px-6 py-4 whitespace-nowrap"><%= l(assignment.assigned_at, format: :long) %></td>
            <td class="px-6 py-4 whitespace-nowrap"><%= assignment.expires_at ? l(assignment.expires_at, format: :long) : "Never" %></td>
          </tr>
        <% end %>
      </tbody>
    </table>
    <% if @assignments.count > 50 %>
      <div class="p-4 text-sm text-stone-600">
        Showing first 50 assignments
      </div>
    <% end %>
  </div>
</div>