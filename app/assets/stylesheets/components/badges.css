/* Badge Component Styles */
@layer components {
  /* CSS Custom Properties for Holographic Effects */
  .badge,
  .badge-compact {
    --holo-primary: 0deg;
    --holo-secondary: 120deg;
    --holo-tertiary: 240deg;
    --holo-intensity: 0.6;
    --holo-speed: 3s;
    --holo-shift: 0deg;
    --metallic-base: linear-gradient(135deg, #e5e7eb 0%, #f3f4f6 25%, #e5e7eb 50%, #d1d5db 75%, #e5e7eb 100%);
  }

  /* Base badge styles with glassy modern appearance and holographic foundation */
  .badge {
    @apply relative inline-flex items-center font-medium rounded-lg border shadow-sm;
    @apply backdrop-filter backdrop-blur-sm bg-opacity-90;
    @apply transition-all duration-200 ease-out;
    @apply transform-gpu will-change-transform;
    @apply cursor-default overflow-hidden;

    /* Default size (medium) */
    @apply px-3 py-1.5 text-sm;

    /* Holographic base layer */
    background-image:
      var(--metallic-base),
      linear-gradient(var(--holo-shift),
        hsl(var(--holo-primary), 70%, 60%) 0%,
        hsl(var(--holo-secondary), 70%, 60%) 33%,
        hsl(var(--holo-tertiary), 70%, 60%) 66%,
        hsl(var(--holo-primary), 70%, 60%) 100%);
    background-blend-mode: overlay, normal;
    background-size: 100% 100%, 200% 200%;
    animation: holographic-shift var(--holo-speed) ease-in-out infinite;
  }

  /* Compact badge variant for search results */
  .badge-compact {
    @apply relative inline-flex items-center font-medium rounded-md border shadow-sm;
    @apply backdrop-filter backdrop-blur-sm bg-opacity-90;
    @apply transition-all duration-200 ease-out;
    @apply transform-gpu will-change-transform;
    @apply cursor-default overflow-hidden;

    /* Compact size */
    @apply px-2 py-0.5 text-xs;
    max-width: 120px;

    /* Holographic base layer for compact badges */
    background-image:
      var(--metallic-base),
      linear-gradient(var(--holo-shift),
        hsl(var(--holo-primary), 70%, 60%) 0%,
        hsl(var(--holo-secondary), 70%, 60%) 33%,
        hsl(var(--holo-tertiary), 70%, 60%) 66%,
        hsl(var(--holo-primary), 70%, 60%) 100%);
    background-blend-mode: overlay, normal;
    background-size: 100% 100%, 200% 200%;
    animation: holographic-shift var(--holo-speed) ease-in-out infinite;
  }

  /* DRAMATIC HOLOGRAPHIC EFFECTS - Highly visible approach */
  .badge:before,
  .badge:after,
  .badge-compact:before,
  .badge-compact:after {
    content: "";
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: inherit;
    pointer-events: none;
    transition: all 0.3s ease;
    z-index: 10;
  }

  /* Primary holographic layer - MAXIMUM VISIBILITY */
  .badge:before,
  .badge-compact:before {
    background: linear-gradient(
      115deg,
      transparent 0%,
      rgba(255, 255, 255, 1) 15%,
      rgba(255, 0, 255, 1) 25%,
      rgba(255, 255, 255, 1) 35%,
      transparent 45%,
      transparent 55%,
      rgba(255, 255, 255, 1) 65%,
      rgba(0, 255, 255, 1) 75%,
      rgba(255, 255, 255, 1) 85%,
      transparent 100%
    );
    background-size: 400% 400%;
    background-position: var(--holo-x, 50%) var(--holo-y, 50%);
    opacity: var(--holo-opacity, 0.9);
    mix-blend-mode: hard-light;
    filter: blur(0.5px);
  }

  /* Secondary rainbow layer - BRIGHT RAINBOW */
  .badge:after,
  .badge-compact:after {
    background: linear-gradient(
      45deg,
      rgba(255, 0, 0, 0.8) 0%,
      rgba(255, 165, 0, 0.8) 16.66%,
      rgba(255, 255, 0, 0.8) 33.33%,
      rgba(0, 255, 0, 0.8) 50%,
      rgba(0, 0, 255, 0.8) 66.66%,
      rgba(75, 0, 130, 0.8) 83.33%,
      rgba(238, 130, 238, 0.8) 100%
    );
    background-size: 200% 200%;
    background-position: calc(var(--holo-x, 50%) * 1.2) calc(var(--holo-y, 50%) * 1.2);
    opacity: calc(0.7 * var(--holo-opacity, 1));
    mix-blend-mode: color-dodge;
    animation: rainbow-shift 3s ease-in-out infinite;
  }

  /* Hover effects for enhanced holographic display */
  .badge:hover:before,
  .badge-compact:hover:before {
    background: linear-gradient(
      110deg,
      transparent 25%,
      rgba(255, 0, 150, 1) 48%,
      rgba(0, 255, 200, 1) 52%,
      transparent 75%
    );
    background-size: 250% 250%;
    opacity: 0.88;
    filter: brightness(0.66) contrast(1.33);
    transition: none;
  }

  .badge:hover:after,
  .badge-compact:hover:after {
    filter: brightness(1) contrast(1);
    opacity: 1;
  }

  .badge:hover,
  .badge-compact:hover {
    box-shadow:
      0 0 30px -5px rgba(255, 255, 255, 0.8),
      0 0 15px -2px rgba(255, 255, 255, 0.6),
      0 8px 25px -8px rgba(0, 0, 0, 0.3);
  }

  /* Active holographic state with enhanced effects */
  .badge.holo-active:before,
  .badge-compact.holo-active:before {
    background: linear-gradient(
      calc(115deg + var(--holo-x, 0) * 0.5),
      transparent 15%,
      rgba(255, 0, 150, 1) 35%,
      rgba(255, 255, 0, 0.9) 50%,
      rgba(0, 255, 200, 1) 65%,
      transparent 85%
    );
    background-size: 200% 200%;
    opacity: calc(var(--holo-opacity, 0.6) * 1.5);
    filter: brightness(0.8) contrast(1.5);
  }

  .badge.holo-active:after,
  .badge-compact.holo-active:after {
    opacity: calc(0.9 * var(--holo-opacity, 1));
    filter: brightness(1.2) contrast(1.2);
  }

  .badge.holo-active,
  .badge-compact.holo-active {
    box-shadow:
      0 0 40px -5px rgba(255, 255, 255, 1),
      0 0 20px -2px rgba(255, 0, 150, 0.6),
      0 0 20px -2px rgba(0, 255, 200, 0.6),
      0 12px 30px -8px rgba(0, 0, 0, 0.4);
  }

  /* Light ray effect - MAXIMUM visibility */
  .badge .light-ray,
  .badge-compact .light-ray {
    position: absolute;
    top: 0;
    left: -50%;
    width: 40%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(255,255,255,0.9) 30%,
      rgba(255,255,255,1) 50%,
      rgba(255,255,255,0.9) 70%,
      transparent 100%);
    animation: light-ray 2s ease-in-out infinite;
    animation-delay: 0.5s;
    pointer-events: none;
    z-index: 4;
    filter: blur(0.3px);
    box-shadow: 0 0 25px rgba(255,255,255,0.9), 0 0 50px rgba(255,255,255,0.6);
  }

  /* Badge icon styling with enhanced holographic effects */
  .badge-icon {
    @apply transition-transform duration-200 ease-out;
    position: relative;
    z-index: 5;
    filter: drop-shadow(0 0 2px rgba(255,255,255,0.3));
  }

  /* Badge name text with enhanced visibility */
  .badge-name {
    @apply font-medium;
    position: relative;
    z-index: 5;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
  }

  /* Compact badge name - no truncation to prevent text cutting */
  .badge-compact .badge-name {
    @apply font-medium;
    /* Removed max-width and truncate to prevent text truncation */
  }

  /* Size variants for full badges */
  .badge-sm {
    @apply px-2 py-1 text-xs;
  }

  .badge-sm .badge-icon {
    @apply text-xs mr-1;
  }

  .badge-lg {
    @apply px-4 py-2.5 text-base;
  }

  .badge-lg .badge-icon {
    @apply text-base mr-2;
  }

  /* Default medium size icon spacing */
  .badge .badge-icon {
    @apply text-sm mr-1.5;
  }

  /* Holographic keyframe animations */
  @keyframes holographic-shift {
    0% {
      --holo-shift: 45deg;
      background-position: 0% 0%, 0% 0%;
    }
    25% {
      --holo-shift: 135deg;
      background-position: 0% 0%, 25% 25%;
    }
    50% {
      --holo-shift: 225deg;
      background-position: 0% 0%, 50% 50%;
    }
    75% {
      --holo-shift: 315deg;
      background-position: 0% 0%, 75% 75%;
    }
    100% {
      --holo-shift: 405deg;
      background-position: 0% 0%, 100% 100%;
    }
  }

  @keyframes sparkle {
    0%, 100% {
      opacity: 0;
      transform: scale(0) rotate(0deg);
    }
    50% {
      opacity: 1;
      transform: scale(1) rotate(180deg);
    }
  }

  @keyframes light-ray {
    0% {
      transform: translateX(-100%) skewX(-15deg);
      opacity: 0;
    }
    50% {
      opacity: 0.8;
    }
    100% {
      transform: translateX(200%) skewX(-15deg);
      opacity: 0;
    }
  }

  @keyframes prismatic-pulse {
    0%, 100% {
      filter: hue-rotate(0deg) saturate(1);
    }
    33% {
      filter: hue-rotate(120deg) saturate(1.2);
    }
    66% {
      filter: hue-rotate(240deg) saturate(1.1);
    }
  }

  /* Enhanced glassy effect overlay with holographic elements */
  .badge::before,
  .badge-compact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      rgba(255,255,255,0.2) 0%,
      rgba(255,255,255,0.1) 25%,
      rgba(255,255,255,0.05) 50%,
      rgba(255,255,255,0.1) 75%,
      rgba(255,255,255,0.15) 100%);
    pointer-events: none;
    border-radius: inherit;
    z-index: 1;
  }

  /* Holographic rainbow overlay - Enhanced visibility */
  .badge::after,
  .badge-compact::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
      rgba(255, 0, 150, 0.25) 0%,
      rgba(0, 255, 255, 0.25) 20%,
      rgba(255, 255, 0, 0.25) 40%,
      rgba(255, 0, 255, 0.25) 60%,
      rgba(0, 255, 150, 0.25) 80%,
      rgba(255, 100, 0, 0.25) 100%);
    background-size: 400% 400%;
    animation: prismatic-pulse 3s ease-in-out infinite;
    pointer-events: none;
    border-radius: inherit;
    opacity: var(--holo-intensity);
    z-index: 2;
    mix-blend-mode: overlay;
  }

  /* Enhanced hover effects with holographic intensification */
  .badge:hover {
    @apply shadow-lg;
    transform: translateY(-3px) scale(1.05);
    --holo-intensity: 1.2;
    --holo-speed: 1s;
    box-shadow:
      0 15px 35px rgba(0, 0, 0, 0.2),
      0 0 40px rgba(255, 255, 255, 0.4),
      0 0 20px rgba(97, 0, 255, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.5);
  }

  .badge:hover::after {
    animation-duration: 1.5s;
    opacity: calc(var(--holo-intensity) * 1.8);
    background-size: 200% 200%;
  }

  .badge:hover .sparkle {
    animation-duration: 0.6s;
    width: 16px;
    height: 16px;
    box-shadow: 0 0 30px rgba(255,255,255,1), 0 0 60px rgba(255,255,255,0.8), 0 0 90px rgba(255,255,255,0.5);
  }

  .badge:hover .light-ray {
    animation-duration: 0.8s;
    opacity: 1;
    width: 60%;
    box-shadow: 0 0 40px rgba(255,255,255,1), 0 0 80px rgba(255,255,255,0.8);
  }

  .badge-compact:hover {
    @apply shadow-md;
    transform: translateY(-1px) scale(1.03);
    --holo-intensity: 0.8;
    --holo-speed: 2s;
    box-shadow:
      0 6px 15px rgba(0, 0, 0, 0.12),
      0 0 20px rgba(255, 255, 255, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.25);
  }

  .badge-compact:hover::after {
    animation-duration: 2.5s;
    opacity: calc(var(--holo-intensity) * 1.3);
  }

  .badge-compact:hover .sparkle {
    animation-duration: 1.2s;
  }

  /* Active/pressed state for enhanced interactivity */
  .badge:active,
  .badge-compact:active {
    transform: translateY(0) scale(0.98);
    --holo-intensity: 1.2;
    transition: all 0.1s ease-out;
  }

  /* Breathing animation for active badges */
  .badge.active,
  .badge-compact.active {
    animation: holographic-shift var(--holo-speed) ease-in-out infinite,
               breathing 2s ease-in-out infinite;
  }

  @keyframes breathing {
    0%, 100% {
      transform: scale(1);
      --holo-intensity: 0.6;
    }
    50% {
      transform: scale(1.02);
      --holo-intensity: 0.9;
    }
  }

  /* Tooltip styles */
  .badge-tooltip,
  .badge-tooltip-compact {
    @apply absolute z-50 invisible opacity-0 transition-all duration-200 ease-out;
    @apply bg-stone-900 text-white text-xs rounded-md px-2 py-1;
    @apply pointer-events-none whitespace-nowrap;
    @apply mt-8 left-1/2 transform -translate-x-1/2;
  }

  .badge-tooltip-compact {
    @apply rounded-lg px-3 py-2 shadow-lg;
    max-width: 200px;
    white-space: normal;
  }

  /* Show tooltips on hover */
  .badge:hover .badge-tooltip,
  .badge-compact:hover .badge-tooltip-compact {
    @apply visible opacity-100;
  }

  /* Tooltip arrows */
  .badge-tooltip::after,
  .badge-tooltip-compact::after {
    content: '';
    position: absolute;
    top: -4px;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-bottom-color: theme('colors.stone.900');
  }

  /* Predefined badge color schemes for common use cases */
  .badge-verified {
    @apply bg-blue-100 bg-opacity-20 text-blue-800 border-blue-200;
  }

  .badge-choice {
    @apply bg-purple-100 bg-opacity-20 text-purple-800 border-purple-200;
  }

  .badge-premium {
    @apply bg-amber-100 bg-opacity-20 text-amber-800 border-amber-200;
  }

  .badge-expert {
    @apply bg-emerald-100 bg-opacity-20 text-emerald-800 border-emerald-200;
  }

  .badge-featured {
    @apply bg-rose-100 bg-opacity-20 text-rose-800 border-rose-200;
  }

  .badge-new {
    @apply bg-green-100 bg-opacity-20 text-green-800 border-green-200;
  }

  /* Icon-only mode for very compact display */
  .badge-compact[data-icon-only="true"] {
    @apply justify-center;
    max-width: 24px;
    min-width: 24px;
  }

  .badge-compact[data-icon-only="true"] .badge-icon {
    @apply mr-0;
  }

  /* Accessibility: Respect reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .badge,
    .badge-compact {
      transition: none !important;
      animation: none !important;
      --holo-speed: 0s;
    }

    .badge::after,
    .badge-compact::after {
      animation: none !important;
      opacity: 0.3 !important;
    }

    .badge .sparkle,
    .badge-compact .sparkle,
    .badge .light-ray,
    .badge-compact .light-ray {
      animation: none !important;
      opacity: 0 !important;
    }

    .badge-icon {
      transition: none !important;
      filter: none !important;
    }

    .badge:hover,
    .badge-compact:hover {
      transform: none !important;
      --holo-intensity: 0.3;
    }

    .badge.active,
    .badge-compact.active {
      animation: none !important;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .badge,
    .badge-compact {
      @apply border-2;
      backdrop-filter: none;
      background-opacity: 1 !important;
    }
  }

  /* Badge Modal Transition Effects */
  .badge-clickable {
    cursor: pointer !important;
    transition: all 0.2s ease-out;
  }

  .badge-clickable:hover {
    transform: scale(1.05) translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.2),
                0 0 20px rgba(255, 255, 255, 0.3);
  }

  .badge-clickable:active {
    transform: scale(0.98);
  }

  /* View Transition Names for smooth modal transitions */
  @supports (view-transition-name: none) {
    .badge-clickable {
      view-transition-name: badge-source;
    }

    .badge-modal-content {
      view-transition-name: badge-modal-content;
    }

    .badge-hero {
      view-transition-name: badge-hero;
    }
  }

  /* Enhanced modal entrance animations */
  @keyframes badgeModalEnter {
    from {
      opacity: 0;
      transform: scale(0.8) translateY(40px) rotateX(15deg);
      filter: blur(10px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0) rotateX(0deg);
      filter: blur(0px);
    }
  }

  @keyframes badgeModalBackdropEnter {
    from {
      opacity: 0;
      backdrop-filter: blur(0px);
    }
    to {
      opacity: 1;
      backdrop-filter: blur(8px);
    }
  }

  .badge-modal-enter {
    animation: badgeModalEnter 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
  }

  .badge-modal-backdrop-enter {
    animation: badgeModalBackdropEnter 0.3s ease-out forwards;
  }

  /* Badge click animation */
  .badge-clicking {
    animation: badgeClick 0.15s ease-out;
  }

  @keyframes badgeClick {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(0.95);
    }
    100% {
      transform: scale(1);
    }
  }

  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    .badge-tooltip,
    .badge-tooltip-compact {
      @apply bg-stone-100 text-stone-900;
    }

    .badge-tooltip::after,
    .badge-tooltip-compact::after {
      border-bottom-color: theme('colors.stone.100');
    }
  }

  /* Mobile responsiveness */
  @media (max-width: 640px) {
    .badge-compact {
      max-width: 100px;
    }
    
    .badge-compact .badge-name {
      max-width: 60px;
    }

    /* Slightly smaller badges on mobile */
    .badge {
      @apply px-2.5 py-1 text-xs;
    }

    .badge .badge-icon {
      @apply text-xs mr-1;
    }
  }

  /* Print styles */
  @media print {
    .badge,
    .badge-compact {
      background: white !important;
      color: black !important;
      border: 1px solid black !important;
      box-shadow: none !important;
      transform: none !important;
    }
    
    .badge::before,
    .badge-compact::before {
      display: none !important;
    }
    
    .badge-tooltip,
    .badge-tooltip-compact {
      display: none !important;
    }
  }

  /* Focus styles for accessibility */
  .badge:focus,
  .badge-compact:focus {
    @apply outline-none ring-2 ring-blue-500 ring-offset-2;
  }

  /* Loading state for dynamic badges */
  .badge-loading {
    @apply animate-pulse bg-stone-200 text-transparent;
  }

  .badge-loading .badge-icon {
    @apply text-transparent;
  }
}
