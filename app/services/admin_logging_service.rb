class AdminLoggingService
  include Singleton

  # Log levels
  DEBUG = 'debug'.freeze
  INFO = 'info'.freeze
  WARN = 'warn'.freeze
  ERROR = 'error'.freeze
  FATAL = 'fatal'.freeze

  # Event types
  USER_ACTION = 'user_action'.freeze
  SYSTEM_EVENT = 'system_event'.freeze
  SECURITY_EVENT = 'security_event'.freeze
  PERFORMANCE_EVENT = 'performance_event'.freeze
  ERROR_EVENT = 'error_event'.freeze

  class << self
    delegate :log_user_action, :log_system_event, :log_security_event, 
             :log_performance_event, :log_error_event, :log_custom_event,
             to: :instance
  end

  def log_user_action(action:, resource: nil, user: nil, details: {})
    log_event(
      level: INFO,
      event_type: USER_ACTION,
      action: action,
      resource: resource,
      user: user || Current.user,
      details: details
    )
  end

  def log_system_event(event:, details: {})
    log_event(
      level: INFO,
      event_type: SYSTEM_EVENT,
      action: event,
      details: details
    )
  end

  def log_security_event(event:, user: nil, severity: WARN, details: {})
    log_event(
      level: severity,
      event_type: SECURITY_EVENT,
      action: event,
      user: user || Current.user,
      details: details
    )
  end

  def log_performance_event(operation:, duration:, details: {})
    log_event(
      level: INFO,
      event_type: PERFORMANCE_EVENT,
      action: operation,
      details: details.merge(duration_ms: duration)
    )
  end

  def log_error_event(error:, context: {})
    log_event(
      level: ERROR,
      event_type: ERROR_EVENT,
      action: 'error_occurred',
      details: {
        error_class: error.class.to_s,
        error_message: error.message,
        error_backtrace: error.backtrace&.first(10),
        context: context
      }
    )
  end

  def log_custom_event(level:, event_type:, action:, resource: nil, user: nil, details: {})
    log_event(
      level: level,
      event_type: event_type,
      action: action,
      resource: resource,
      user: user,
      details: details
    )
  end

  private

  def log_event(level:, event_type:, action:, resource: nil, user: nil, details: {})
    log_data = build_log_data(
      level: level,
      event_type: event_type,
      action: action,
      resource: resource,
      user: user,
      details: details
    )

    # Log to Rails logger with structured format
    Rails.logger.send(level.to_sym, format_log_message(log_data))

    # Store in database for admin events
    store_admin_log(log_data) if should_store_in_database?(event_type, level)

    # Send to external monitoring if configured
    send_to_monitoring(log_data) if should_send_to_monitoring?(event_type, level)

    log_data
  end

  def build_log_data(level:, event_type:, action:, resource: nil, user: nil, details: {})
    {
      timestamp: Time.current.iso8601,
      level: level,
      event_type: event_type,
      action: action,
      admin_interface: true,
      user: user_data(user),
      resource: resource_data(resource),
      request: request_data,
      session: session_data,
      details: details
    }
  end

  def user_data(user)
    return nil unless user

    {
      id: user.id,
      email: user.email,
      admin_role: user.admin_role,
      impersonating: Current.impersonating?,
      impersonator_id: Current.impersonator_id
    }
  end

  def resource_data(resource)
    return nil unless resource

    {
      type: resource.class.name,
      id: resource.respond_to?(:id) ? resource.id : nil,
      identifier: resource_identifier(resource)
    }
  end

  def resource_identifier(resource)
    case resource
    when User
      resource.email
    when Job
      resource.title
    when Organization
      resource.name
    when AdminAuditLog
      "#{resource.action} - #{resource.created_at}"
    else
      resource.respond_to?(:name) ? resource.name : resource.to_s
    end
  end

  def request_data
    return {} unless defined?(Current) && Current.respond_to?(:request_id)

    {
      id: Current.request_id,
      ip_address: Current.ip_address,
      user_agent: Current.user_agent,
      path: Current.request_path,
      method: Current.request_method
    }
  end

  def session_data
    return {} unless Current.respond_to?(:session)

    {
      id: Current.session&.id,
      created_at: Current.session&.created_at
    }
  end

  def format_log_message(log_data)
    "[ADMIN] #{log_data[:level].upcase} #{log_data[:event_type]} - #{log_data[:action]} | #{log_data.to_json}"
  end

  def should_store_in_database?(event_type, level)
    # Store security events and errors in database
    [SECURITY_EVENT, ERROR_EVENT].include?(event_type) || 
    [ERROR, FATAL].include?(level)
  end

  def should_send_to_monitoring?(event_type, level)
    # Send errors and security events to external monitoring
    [SECURITY_EVENT, ERROR_EVENT].include?(event_type) || 
    [ERROR, FATAL].include?(level)
  end

  def store_admin_log(log_data)
    AdminAuditLog.log_action(
      action: "#{log_data[:event_type]}_#{log_data[:action]}",
      controller: 'admin_logging_service',
      resource: nil,
      changes: {
        'log_level' => [nil, log_data[:level]],
        'event_type' => [nil, log_data[:event_type]],
        'details' => [nil, log_data[:details]]
      },
      admin_user: Current.user
    )
  rescue => e
    Rails.logger.error "Failed to store admin log in database: #{e.message}"
  end

  def send_to_monitoring(log_data)
    # Send to Sentry for errors
    if [ERROR, FATAL].include?(log_data[:level])
      Sentry.with_scope do |scope|
        scope.set_tag('component', 'admin_logging')
        scope.set_tag('event_type', log_data[:event_type])
        scope.set_user(log_data[:user]) if log_data[:user]
        scope.set_context('admin_log', log_data)
        
        Sentry.capture_message(
          "Admin #{log_data[:event_type]}: #{log_data[:action]}",
          level: log_data[:level].to_sym
        )
      end
    end

    # Send to PostHog for analytics (if configured)
    send_to_posthog(log_data) if defined?(PostHog)
  rescue => e
    Rails.logger.error "Failed to send admin log to monitoring: #{e.message}"
  end

  def send_to_posthog(log_data)
    return unless log_data[:user]

    PostHog.capture(
      distinct_id: log_data[:user][:id],
      event: "admin_#{log_data[:event_type]}",
      properties: {
        action: log_data[:action],
        admin_role: log_data[:user][:admin_role],
        resource_type: log_data[:resource]&.dig(:type),
        impersonating: log_data[:user][:impersonating],
        timestamp: log_data[:timestamp]
      }.merge(log_data[:details])
    )
  rescue => e
    Rails.logger.error "Failed to send admin log to PostHog: #{e.message}"
  end
end
