# == Schema Information
#
# Table name: admin_audit_logs
#
#  id            :bigint           not null, primary key
#  action        :string           not null
#  change_data   :json
#  controller    :string           not null
#  ip_address    :string
#  resource_type :string
#  user_agent    :string
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  admin_user_id :bigint           not null
#  resource_id   :bigint
#
# Indexes
#
#  index_admin_audit_logs_on_action                         (action)
#  index_admin_audit_logs_on_admin_user_id                  (admin_user_id)
#  index_admin_audit_logs_on_admin_user_id_and_created_at   (admin_user_id,created_at)
#  index_admin_audit_logs_on_controller                     (controller)
#  index_admin_audit_logs_on_created_at                     (created_at)
#  index_admin_audit_logs_on_resource                       (resource_type,resource_id)
#  index_admin_audit_logs_on_resource_type_and_created_at   (resource_type,created_at)
#  index_admin_audit_logs_on_resource_type_and_resource_id  (resource_type,resource_id)
#
# Foreign Keys
#
#  fk_rails_...  (admin_user_id => users.id)
#
class AdminAuditLog < ApplicationRecord
  belongs_to :admin_user, class_name: 'User'
  belongs_to :resource, polymorphic: true, optional: true

  validates :action, presence: true
  validates :controller, presence: true
  validates :admin_user_id, presence: true

  # Common admin actions
  ACTIONS = %w[
    create
    read
    update
    delete
    export
    search
    filter
    assign_role
    remove_role
    impersonate
    bulk_update
    bulk_delete
    password_reset
    account_activate
    account_deactivate
    badge_type_created
    badge_type_updated
    badge_type_deleted
    badge_type_create_failed
    badge_type_update_failed
    badge_type_delete_blocked
    badge_assigned
    badge_revoked
    badge_assignment_failed
  ].freeze

  validates :action, inclusion: { in: ACTIONS }

  scope :recent, -> { where('created_at > ?', 30.days.ago) }
  scope :by_admin, ->(admin_id) { where(admin_user_id: admin_id) }
  scope :by_action, ->(action) { where(action: action) }
  scope :by_resource_type, ->(type) { where(resource_type: type) }
  scope :by_controller, ->(controller) { where(controller: controller) }
  scope :with_changes, -> { where.not(change_data: nil) }

  # Class method to log admin actions
  def self.log_action(action:, controller:, resource: nil, changes: nil, admin_user: nil)
    admin_user ||= Current.user
    return unless admin_user&.admin?

    create!(
      action: action,
      controller: controller,
      resource: resource,
      change_data: changes,
      admin_user: admin_user,
      ip_address: Current.ip_address,
      user_agent: Current.user_agent
    )
  rescue => e
    Rails.logger.error "Failed to log admin action: #{e.message}"
    # Don't raise the error to avoid breaking the main action
  end

  # Instance methods
  def resource_name
    return 'Unknown' unless resource_type
    resource_type.humanize
  end

  def resource_identifier
    if resource
      case resource
      when User
        "#{resource.full_name} (#{resource.email})"
      when Organization
        resource.name
      when Job
        resource.title
      when Role
        resource.name
      else
        resource.try(:name) || resource.try(:title) || "##{resource.id}"
      end
    elsif resource_type && resource_id
      # Handle deleted resources
      "#{resource_type} ##{resource_id}"
    else
      'N/A'
    end
  end

  def formatted_changes
    return {} unless change_data.present?

    formatted = {}
    change_data.each do |key, value_array|
      next unless value_array.is_a?(Array) && value_array.length == 2

      old_value, new_value = value_array
      formatted[key.humanize] = {
        from: format_value(old_value),
        to: format_value(new_value)
      }
    end
    formatted
  end

  def admin_name
    admin_user&.full_name || 'Unknown Admin'
  end

  def admin_email
    admin_user&.email || '<EMAIL>'
  end

  def description
    case action
    when 'create'
      "Created #{resource_name.downcase} #{resource_identifier}"
    when 'update'
      "Updated #{resource_name.downcase} #{resource_identifier}"
    when 'delete'
      "Deleted #{resource_name.downcase} #{resource_identifier}"
    when 'assign_role'
      "Assigned role to #{resource_identifier}"
    when 'remove_role'
      "Removed role from #{resource_identifier}"
    when 'impersonate'
      "Started impersonating #{resource_identifier}"
    when 'export'
      "Exported #{resource_name.downcase} data"
    when 'bulk_update'
      "Performed bulk update on #{resource_name.downcase} records"
    when 'bulk_delete'
      "Performed bulk delete on #{resource_name.downcase} records"
    when 'password_reset'
      "Reset password for #{resource_identifier}"
    when 'account_activate'
      "Activated account for #{resource_identifier}"
    when 'account_deactivate'
      "Deactivated account for #{resource_identifier}"
    else
      "Performed #{action.humanize.downcase} on #{resource_name.downcase} #{resource_identifier}"
    end
  end

  private

  def format_value(value)
    case value
    when true, false
      value.to_s.capitalize
    when nil
      'None'
    when Time, DateTime, Date
      value.strftime('%B %d, %Y at %I:%M %p')
    else
      value.to_s
    end
  end
end
