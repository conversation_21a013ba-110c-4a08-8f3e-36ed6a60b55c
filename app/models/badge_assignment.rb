# == Schema Information
#
# Table name: badge_assignments
#
#  id            :bigint           not null, primary key
#  assigned_at   :datetime         not null
#  expires_at    :datetime
#  notes         :text
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  admin_id      :bigint           not null
#  badge_type_id :bigint           not null
#  user_id       :bigint           not null
#
# Indexes
#
#  index_badge_assignments_active_by_type                    (badge_type_id) WHERE (expires_at IS NULL)
#  index_badge_assignments_active_only                       (user_id,badge_type_id,assigned_at) WHERE (expires_at IS NULL)
#  index_badge_assignments_on_admin_and_date                 (admin_id,assigned_at)
#  index_badge_assignments_on_assigned_at                    (assigned_at)
#  index_badge_assignments_on_assigning_admin                (admin_id)
#  index_badge_assignments_on_badge_type_id                  (badge_type_id)
#  index_badge_assignments_on_badge_type_id_and_assigned_at  (badge_type_id,assigned_at)
#  index_badge_assignments_on_badge_type_id_and_user_id      (badge_type_id,user_id) UNIQUE
#  index_badge_assignments_on_expires_at                     (expires_at)
#  index_badge_assignments_on_type_date_expiry               (badge_type_id,assigned_at,expires_at)
#  index_badge_assignments_on_user_active_priority           (user_id,assigned_at,expires_at)
#  index_badge_assignments_on_user_id                        (user_id)
#  index_badge_assignments_on_user_id_and_expires_at         (user_id,expires_at)
#
# Foreign Keys
#
#  fk_rails_...  (admin_id => users.id)
#  fk_rails_...  (badge_type_id => badge_types.id)
#  fk_rails_...  (user_id => users.id)
#
class BadgeAssignment < ApplicationRecord
  # Associations
  belongs_to :badge_type
  belongs_to :user
  belongs_to :admin, class_name: 'User', foreign_key: 'admin_id'

  # Validations
  validates :assigned_at, presence: true
  validates :badge_type_id, uniqueness: { 
    scope: :user_id, 
    message: 'has already been assigned to this user' 
  }
  validates :notes, length: { maximum: 1000 }
  
  # Custom validations
  validate :admin_must_have_badge_permissions
  validate :expires_at_must_be_in_future, if: :expires_at?
  validate :assigned_at_cannot_be_in_future

  # Callbacks
  before_validation :set_assigned_at, on: :create
  after_create :log_badge_assignment
  after_destroy :log_badge_removal

  # Scopes
  scope :active, -> { where('expires_at IS NULL OR expires_at > ?', Time.current) }
  scope :expired, -> { where('expires_at IS NOT NULL AND expires_at <= ?', Time.current) }
  scope :permanent, -> { where(expires_at: nil) }
  scope :temporary, -> { where.not(expires_at: nil) }
  scope :recent, -> { order(assigned_at: :desc) }
  scope :by_priority, -> { joins(:badge_type).order('badge_types.priority ASC, badge_assignments.assigned_at DESC') }
  scope :assigned_by, ->(admin) { where(admin: admin) }
  scope :for_badge_type, ->(badge_type) { where(badge_type: badge_type) }
  scope :for_user, ->(user) { where(user: user) }

  # Class methods
  def self.active_for_user(user)
    active.where(user: user).includes(:badge_type).by_priority
  end

  def self.expired_assignments
    expired.includes(:badge_type, :user, :admin)
  end

  def self.assignments_by_admin(admin, limit: 50)
    assigned_by(admin).recent.limit(limit).includes(:badge_type, :user)
  end

  def self.assignments_for_badge_type(badge_type)
    for_badge_type(badge_type).includes(:user, :admin).recent
  end

  def self.cleanup_expired_assignments
    expired.destroy_all
  end

  # Instance methods
  def active?
    expires_at.nil? || expires_at > Time.current
  end

  def expired?
    !active?
  end

  def permanent?
    expires_at.nil?
  end

  def temporary?
    !permanent?
  end

  def expires_in_days
    return nil if permanent?
    return 0 if expired?
    
    ((expires_at - Time.current) / 1.day).ceil
  end

  def days_since_assigned
    ((Time.current - assigned_at) / 1.day).floor
  end

  def assigned_by_name
    admin.full_name
  end

  def badge_name
    badge_type.name
  end

  def user_name
    user.full_name
  end

  def assignment_summary
    summary = "#{badge_name} badge assigned to #{user_name}"
    summary += " by #{assigned_by_name}"
    summary += " on #{assigned_at.strftime('%B %d, %Y')}"
    summary += expires_at ? " (expires #{expires_at.strftime('%B %d, %Y')})" : " (permanent)"
    summary
  end

  def can_be_removed_by?(admin_user)
    return false unless admin_user&.admin?
    
    # Super admins can remove any badge
    return true if admin_user.superadmin?
    
    # Admins can only remove badges they assigned (unless it's a super admin assignment)
    return false if admin.superadmin? && !admin_user.superadmin?
    
    # Support admins can remove badges they assigned
    admin_user.has_role?(:support) && admin == admin_user
  end

  def extend_expiration(new_expires_at, extending_admin)
    return false unless extending_admin&.admin?
    return false if new_expires_at && new_expires_at <= Time.current
    
    old_expires_at = expires_at
    update(expires_at: new_expires_at)
    
    if saved_change_to_expires_at?
      log_badge_extension(old_expires_at, new_expires_at, extending_admin)
      true
    else
      false
    end
  end

  # Test helper method to allow creating expired badges for testing
  def allow_expired_for_testing!
    @allow_expired_for_testing = true
    self
  end

  private

  def set_assigned_at
    self.assigned_at ||= Time.current
  end

  def admin_must_have_badge_permissions
    return unless admin
    
    unless admin.admin?
      errors.add(:admin, 'must have administrative privileges to assign badges')
    end
  end

  def expires_at_must_be_in_future
    return unless expires_at
    # Allow expired dates in test environment only when explicitly creating test data
    # This is controlled by a special attribute that can be set in tests
    return if Rails.env.test? && @allow_expired_for_testing

    if expires_at <= Time.current
      errors.add(:expires_at, 'must be in the future')
    end
  end

  def assigned_at_cannot_be_in_future
    return unless assigned_at
    
    if assigned_at > Time.current
      errors.add(:assigned_at, 'cannot be in the future')
    end
  end

  def log_badge_assignment
    AdminAuditLog.log_action(
      action: 'create',
      controller: 'BadgeAssignmentsController',
      resource: self,
      changes: {
        'badge_type' => [nil, badge_type.name],
        'user' => [nil, user.full_name],
        'expires_at' => [nil, expires_at],
        'notes' => [nil, notes]
      },
      admin_user: admin
    )
  rescue => e
    Rails.logger.error "Failed to log badge assignment: #{e.message}"
  end

  def log_badge_removal
    AdminAuditLog.log_action(
      action: 'delete',
      controller: 'BadgeAssignmentsController',
      resource: self,
      changes: {
        'badge_type' => [badge_type.name, nil],
        'user' => [user.full_name, nil],
        'assigned_at' => [assigned_at, nil],
        'removed_at' => [nil, Time.current]
      },
      admin_user: admin
    )
  rescue => e
    Rails.logger.error "Failed to log badge removal: #{e.message}"
  end

  def log_badge_extension(old_expires_at, new_expires_at, extending_admin)
    AdminAuditLog.log_action(
      action: 'update',
      controller: 'BadgeAssignmentsController',
      resource: self,
      changes: {
        'expires_at' => [old_expires_at, new_expires_at],
        'extended_by' => [nil, extending_admin.full_name]
      },
      admin_user: extending_admin
    )
  rescue => e
    Rails.logger.error "Failed to log badge extension: #{e.message}"
  end
end
